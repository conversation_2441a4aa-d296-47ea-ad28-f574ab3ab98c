use crate::dwd::util::dwd_common_util::DwdCommonUtil;
use crate::utils::decimal::{Decimal38_18, IntoDecimal38_18};
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};

#[derive(Row, Serialize, Deserialize, Debug, PartialEq)]
#[allow(non_snake_case)]
pub struct TestItemDetailRow<'a> {
    pub ID: &'a str,
    pub CUSTOMER: &'a str,
    pub SUB_CUSTOMER: &'a str,
    pub UPLOAD_TYPE: &'a str,
    pub FILE_ID: u32,
    pub FILE_NAME: &'a str,
    pub FILE_TYPE: &'a str,
    pub DEVICE_ID: &'a str,
    pub FACTORY: &'a str,
    pub FACTORY_SITE: &'a str,
    pub FAB: &'a str,
    pub FAB_SITE: &'a str,
    pub LOT_TYPE: &'a str,
    pub LOT_ID: &'a str,
    pub PROCESS: &'a str,
    pub SBLOT_ID: &'a str,
    pub WAFER_LOT_ID: &'a str,
    pub TEST_AREA: &'a str,
    pub TEST_STAGE: &'a str,
    pub OFFLINE_RETEST: Option<u8>,
    pub ONLINE_RETEST: Option<u8>,
    pub INTERRUPT: Option<u8>,
    pub DUP_RETEST: Option<u8>,
    pub BATCH_NUM: Option<u8>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<u8>,
    pub INTERRUPT_IGNORE_TP: Option<u8>,
    pub DUP_RETEST_IGNORE_TP: Option<u8>,
    pub BATCH_NUM_IGNORE_TP: Option<u8>,
    pub MAX_OFFLINE_RETEST: Option<u8>,
    pub MAX_ONLINE_RETEST: Option<u8>,
    pub IS_DIE_FIRST_TEST: Option<u8>,
    pub IS_DIE_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST: Option<u8>,
    pub IS_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FIRST_TEST: Option<u8>,
    pub IS_DUP_FINAL_TEST: Option<u8>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub TEST_SUITE: String,
    pub CONDITION_SET: Vec<(String, String)>,
    pub TEST_NUM: Option<u32>,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub IS_DIE_FIRST_TEST_ITEM: Option<u8>,
    pub TESTITEM_TYPE: String,
    pub TEST_FLG: String,
    pub PARM_FLG: String,
    pub TEST_STATE: String,
    pub TEST_VALUE: Option<Decimal38_18>,
    pub UNITS: String,
    pub TEST_RESULT: Option<u8>,
    pub ORIGIN_TEST_VALUE: Option<Decimal38_18>,
    pub ORIGIN_UNITS: String,
    pub TEST_ORDER: Option<u32>,
    pub ALARM_ID: String,
    pub OPT_FLG: String,
    pub RES_SCAL: Option<i32>,
    pub NUM_TEST: Option<i32>,
    pub TEST_PROGRAM: &'a str,
    pub TEST_TEMPERATURE: &'a str,
    pub TEST_PROGRAM_VERSION: &'a str,
    pub LLM_SCAL: Option<i32>,
    pub HLM_SCAL: Option<i32>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub C_RESFMT: String,
    pub C_LLMFMT: String,
    pub C_HLMFMT: String,
    pub LO_SPEC: Option<Decimal38_18>,
    pub HI_SPEC: Option<Decimal38_18>,
    pub SPEC_NAM: &'a str,
    pub SPEC_VER: &'a str,
    pub HBIN_NUM: Option<u32>,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub HBIN: String,
    pub SBIN: String,
    pub TEST_HEAD: Option<u32>,
    pub TESTER_NAME: &'a str,
    pub TESTER_TYPE: &'a str,
    pub OPERATOR_NAME: &'a str,
    pub PROBER_HANDLER_TYP: &'a str,
    pub PROBER_HANDLER_ID: &'a str,
    pub PROBECARD_LOADBOARD_TYP: &'a str,
    pub PROBECARD_LOADBOARD_ID: &'a str,
    pub PART_FLG: String,
    pub PART_ID: String,
    pub C_PART_ID: Option<u32>,
    pub UID: String,
    pub ECID: String,
    pub ECID_EXT: String,
    pub ECID_EXTRA: Vec<(String, String)>,
    pub IS_STANDARD_ECID: Option<u8>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub TEST_TIME: Option<u32>,
    pub PART_TXT: String,
    pub PART_FIX: String,
    pub TOUCH_DOWN_ID: Option<u32>,
    pub SITE: Option<u32>,
    pub SITE_GRP: Option<u32>,
    pub SITE_CNT: Option<u32>,
    pub SITE_NUMS: &'a str,
    pub TEXT_DAT: String,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub START_TIME: Option<DateTime<Utc>>,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub END_TIME: Option<DateTime<Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub WAFER_ID: String,
    pub WAFER_NO: String,
    pub WAFER_SIZE: Option<Decimal38_18>,
    pub WAFER_MARGIN: Option<Decimal38_18>,
    pub DIE_HEIGHT: Option<Decimal38_18>,
    pub DIE_WIDTH: Option<Decimal38_18>,
    pub WF_UNITS: Option<u32>,
    pub WF_FLAT: &'a str,
    pub CENTER_X: Option<i32>,
    pub CENTER_Y: Option<i32>,
    pub CENTER_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub CENTER_RETICLE_X: Option<i32>,
    pub CENTER_RETICLE_Y: Option<u8>,
    pub CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub POS_X: &'a str,
    pub POS_Y: &'a str,
    pub DIE_CNT: Option<u32>,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub RETICLE_ROW: Option<u32>,
    pub RETICLE_COLUMN: Option<u32>,
    pub RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_WAFER_SIZE: Option<Decimal38_18>,
    pub ORIGINAL_WAFER_MARGIN: Option<Decimal38_18>,
    pub ORIGINAL_WF_UNITS: Option<u32>,
    pub ORIGINAL_WF_FLAT: &'a str,
    pub ORIGINAL_POS_X: &'a str,
    pub ORIGINAL_POS_Y: &'a str,
    pub ORIGINAL_DIE_WIDTH: Option<Decimal38_18>,
    pub ORIGINAL_DIE_HEIGHT: Option<Decimal38_18>,
    pub ORIGINAL_RETICLE_ROW: Option<u32>,
    pub ORIGINAL_RETICLE_COLUMN: Option<u32>,
    pub ORIGINAL_RETICLE_ROW_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: Option<i32>,
    pub ORIGINAL_CENTER_X: Option<i32>,
    pub ORIGINAL_CENTER_Y: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_X: Option<i32>,
    pub ORIGINAL_CENTER_RETICLE_Y: Option<u8>,
    pub ORIGINAL_CENTER_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_OFFSET_Y: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_X: Option<Decimal38_18>,
    pub ORIGINAL_CENTER_RETICLE_OFFSET_Y: Option<Decimal38_18>,
    pub SITE_ID: String,
    pub PART_CNT: Option<u32>,
    pub RTST_CNT: Option<u32>,
    pub ABRT_CNT: Option<u32>,
    pub GOOD_CNT: Option<u32>,
    pub FUNC_CNT: Option<u32>,
    pub FABWF_ID: &'a str,
    pub FRAME_ID: &'a str,
    pub MASK_ID: &'a str,
    pub WAFER_USR_DESC: &'a str,
    pub WAFER_EXC_DESC: &'a str,
    #[serde(with = "clickhouse::serde::chrono::datetime::option")]
    pub SETUP_T: Option<DateTime<Utc>>,
    pub STAT_NUM: Option<u32>,
    pub MODE_COD: &'a str,
    pub PROT_COD: &'a str,
    pub BURN_TIM: Option<u32>,
    pub CMOD_COD: &'a str,
    pub EXEC_TYP: &'a str,
    pub EXEC_VER: &'a str,
    pub USER_TXT: &'a str,
    pub AUX_FILE: &'a str,
    pub PKG_TYP: &'a str,
    pub FAMLY_ID: &'a str,
    pub DATE_COD: &'a str,
    pub FACIL_ID: &'a str,
    pub FLOOR_ID: &'a str,
    pub PROC_ID: &'a str,
    pub OPER_FRQ: &'a str,
    pub FLOW_ID: &'a str,
    pub FLOW_ID_IGNORE_TP: &'a str,
    pub SETUP_ID: &'a str,
    pub DSGN_REV: &'a str,
    pub ENG_ID: &'a str,
    pub ROM_COD: &'a str,
    pub SERL_NUM: &'a str,
    pub SUPR_NAM: &'a str,
    pub DISP_COD: &'a str,
    pub LOT_USR_DESC: &'a str,
    pub LOT_EXC_DESC: &'a str,
    pub DIB_TYP: &'a str,
    pub DIB_ID: &'a str,
    pub CABL_TYP: &'a str,
    pub CABL_ID: &'a str,
    pub CONT_TYP: &'a str,
    pub CONT_ID: &'a str,
    pub LASR_TYP: &'a str,
    pub LASR_ID: &'a str,
    pub EXTR_TYP: &'a str,
    pub EXTR_ID: &'a str,
    pub EFUSE_EXTRA: Vec<(String, String)>,
    pub CHIP_ID: String,
    pub RETEST_BIN_NUM: &'a str,
    pub VECT_NAM: String,
    pub TIME_SET: String,
    pub NUM_FAIL: Option<u32>,
    pub FAIL_PIN: String,
    pub CYCL_CNT: Option<u32>,
    pub REPT_CNT: Option<u32>,
    pub LONG_ATTRIBUTE_SET: Vec<(String, i64)>,
    pub STRING_ATTRIBUTE_SET: Vec<(String, String)>,
    pub FLOAT_ATTRIBUTE_SET: Vec<(String, Decimal38_18)>,
    pub CREATE_HOUR_KEY: String,
    pub CREATE_DAY_KEY: String,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub CREATE_TIME: DateTime<Utc>,
    pub CREATE_USER: &'a str,
    #[serde(with = "clickhouse::serde::chrono::datetime")]
    pub UPLOAD_TIME: DateTime<Utc>,
    pub DATA_VERSION: i64,
    pub LOT_BUCKET: i32,
    pub IS_DELETE: u8,
}

use crate::dto::dwd::{file_detail::FileDetail, sub_test_item_detail::SubTestItemDetail};
use crate::utils::date::IntoDateTimeUtc;

const EMPTY: &str = "";
impl<'a> TestItemDetailRow<'a> {
    pub fn new(sub_test_item_detail: &'static SubTestItemDetail, file_detail: &'a FileDetail) -> Self {
        Self {
            ID: EMPTY,
            CUSTOMER: &file_detail.CUSTOMER,
            SUB_CUSTOMER: &file_detail.SUB_CUSTOMER,
            UPLOAD_TYPE: &file_detail.UPLOAD_TYPE,
            FILE_ID: sub_test_item_detail.FILE_ID.unwrap() as u32,
            FILE_NAME: &file_detail.FILE_NAME,
            FILE_TYPE: &file_detail.FILE_TYPE,
            DEVICE_ID: &file_detail.DEVICE_ID,
            FACTORY: &file_detail.FACTORY,
            FACTORY_SITE: &file_detail.FACTORY_SITE,
            FAB: &file_detail.FAB,
            FAB_SITE: &file_detail.FAB_SITE,
            LOT_TYPE: &file_detail.LOT_TYPE,
            LOT_ID: &file_detail.LOT_ID,
            SBLOT_ID: &file_detail.SBLOT_ID,
            WAFER_LOT_ID: sub_test_item_detail.WAFER_LOT_ID.as_ref().unwrap(),
            TEST_AREA: &file_detail.TEST_AREA,
            TEST_STAGE: &file_detail.TEST_STAGE,
            OFFLINE_RETEST: file_detail.OFFLINE_RETEST,
            OFFLINE_RETEST_IGNORE_TP: file_detail.OFFLINE_RETEST_IGNORE_TP,
            ONLINE_RETEST: sub_test_item_detail.ONLINE_RETEST.map(|v| v as u8),
            INTERRUPT: file_detail.INTERRUPT,
            INTERRUPT_IGNORE_TP: file_detail.INTERRUPT_IGNORE_TP,
            DUP_RETEST: file_detail.DUP_RETEST,
            DUP_RETEST_IGNORE_TP: file_detail.DUP_RETEST_IGNORE_TP,
            BATCH_NUM: file_detail.BATCH_NUM,
            BATCH_NUM_IGNORE_TP: file_detail.BATCH_NUM_IGNORE_TP,
            MAX_OFFLINE_RETEST: sub_test_item_detail.MAX_OFFLINE_RETEST.map(|v| v as u8),
            MAX_ONLINE_RETEST: sub_test_item_detail.MAX_ONLINE_RETEST.map(|v| v as u8),
            IS_DIE_FIRST_TEST: sub_test_item_detail.IS_DIE_FIRST_TEST.map(|v| v as u8),
            IS_DIE_FINAL_TEST: sub_test_item_detail.IS_DIE_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST: sub_test_item_detail.IS_FIRST_TEST.map(|v| v as u8),
            IS_FINAL_TEST: sub_test_item_detail.IS_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST_IGNORE_TP: sub_test_item_detail.IS_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_FINAL_TEST_IGNORE_TP: sub_test_item_detail.IS_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FIRST_TEST: sub_test_item_detail.IS_DUP_FIRST_TEST.map(|v| v as u8),
            IS_DUP_FINAL_TEST: sub_test_item_detail.IS_DUP_FINAL_TEST.map(|v| v as u8),
            IS_DUP_FIRST_TEST_IGNORE_TP: sub_test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FINAL_TEST_IGNORE_TP: sub_test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            TEST_SUITE: sub_test_item_detail.TEST_SUITE.clone().unwrap(),
            CONDITION_SET: sub_test_item_detail
                .CONDITION_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            TEST_NUM: sub_test_item_detail.TEST_NUM.map(|v| v as u32),
            TEST_TXT: sub_test_item_detail.TEST_TXT.clone().unwrap(),
            TEST_ITEM: sub_test_item_detail.TEST_ITEM.clone().unwrap(),
            IS_DIE_FIRST_TEST_ITEM: sub_test_item_detail.IS_DIE_FIRST_TEST_ITEM.map(|v| v as u8),
            TESTITEM_TYPE: sub_test_item_detail.TESTITEM_TYPE.clone().unwrap(),
            TEST_FLG: sub_test_item_detail.TEST_FLG.clone().unwrap(),
            PARM_FLG: sub_test_item_detail.PARM_FLG.clone().unwrap(),
            TEST_STATE: sub_test_item_detail.TEST_STATE.clone().unwrap(),
            TEST_VALUE: sub_test_item_detail.TEST_VALUE.map(|v| v.into_decimal38_18()),
            UNITS: sub_test_item_detail.UNITS.clone().unwrap(),
            TEST_RESULT: sub_test_item_detail.TEST_RESULT.map(|v| v as u8),
            ORIGIN_TEST_VALUE: sub_test_item_detail.ORIGIN_TEST_VALUE.map(|v| v.into_decimal38_18()),
            ORIGIN_UNITS: sub_test_item_detail.ORIGIN_UNITS.clone().unwrap(),
            TEST_ORDER: sub_test_item_detail.TEST_ORDER.map(|v| v as u32),
            ALARM_ID: sub_test_item_detail.ALARM_ID.clone().unwrap(),
            OPT_FLG: sub_test_item_detail.OPT_FLG.clone().unwrap(),
            RES_SCAL: sub_test_item_detail.RES_SCAL,
            NUM_TEST: sub_test_item_detail.NUM_TEST,
            TEST_PROGRAM: &file_detail.TEST_PROGRAM,
            TEST_TEMPERATURE: &file_detail.TEST_TEMPERATURE,
            TEST_PROGRAM_VERSION: &file_detail.TEST_PROGRAM_VERSION,
            LLM_SCAL: sub_test_item_detail.LLM_SCAL,
            HLM_SCAL: sub_test_item_detail.HLM_SCAL,
            LO_LIMIT: sub_test_item_detail.LO_LIMIT.map(|v| v.into_decimal38_18()),
            HI_LIMIT: sub_test_item_detail.HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_HI_LIMIT: sub_test_item_detail.ORIGIN_HI_LIMIT.map(|v| v.into_decimal38_18()),
            ORIGIN_LO_LIMIT: sub_test_item_detail.ORIGIN_LO_LIMIT.map(|v| v.into_decimal38_18()),
            C_RESFMT: sub_test_item_detail.C_RESFMT.clone().unwrap(),
            C_LLMFMT: sub_test_item_detail.C_LLMFMT.clone().unwrap(),
            C_HLMFMT: sub_test_item_detail.C_HLMFMT.clone().unwrap(),
            LO_SPEC: sub_test_item_detail.LO_SPEC.map(|v| v.into_decimal38_18()),
            HI_SPEC: sub_test_item_detail.HI_SPEC.map(|v| v.into_decimal38_18()),
            SPEC_NAM: &file_detail.SPEC_NAM,
            SPEC_VER: &file_detail.SPEC_VER,
            HBIN_NUM: sub_test_item_detail.HBIN_NUM.map(|v| v as u32),
            SBIN_NUM: sub_test_item_detail.SBIN_NUM.map(|v| v as u32),
            SBIN_PF: sub_test_item_detail.SBIN_PF.clone().unwrap(),
            SBIN_NAM: sub_test_item_detail.SBIN_NAM.clone().unwrap(),
            HBIN_PF: sub_test_item_detail.HBIN_PF.clone().unwrap(),
            HBIN_NAM: sub_test_item_detail.HBIN_NAM.clone().unwrap(),
            HBIN: sub_test_item_detail.HBIN.clone().unwrap(),
            SBIN: sub_test_item_detail.SBIN.clone().unwrap(),
            TEST_HEAD: sub_test_item_detail.TEST_HEAD.map(|v| v as u32),
            TESTER_NAME: &file_detail.TESTER_NAME,
            TESTER_TYPE: &file_detail.TESTER_TYPE,
            OPERATOR_NAME: &file_detail.OPERATOR_NAME,
            PROBER_HANDLER_TYP: &file_detail.PROBER_HANDLER_TYP,
            PROBER_HANDLER_ID: &file_detail.PROBER_HANDLER_ID,
            PROBECARD_LOADBOARD_TYP: &file_detail.PROBECARD_LOADBOARD_TYP,
            PROBECARD_LOADBOARD_ID: &file_detail.PROBECARD_LOADBOARD_ID,
            PART_FLG: sub_test_item_detail.PART_FLG.clone().unwrap(),
            PART_ID: sub_test_item_detail.PART_ID.clone().unwrap(),
            C_PART_ID: sub_test_item_detail.C_PART_ID.map(|v| v as u32),
            ECID: sub_test_item_detail.ECID.clone().unwrap(),
            ECID_EXT: sub_test_item_detail.ECID_EXT.clone().unwrap(),
            ECID_EXTRA: sub_test_item_detail
                .ECID_EXTRA
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            IS_STANDARD_ECID: sub_test_item_detail.IS_STANDARD_ECID.map(|v| v as u8),
            X_COORD: sub_test_item_detail.X_COORD,
            Y_COORD: sub_test_item_detail.Y_COORD,
            DIE_X: sub_test_item_detail.DIE_X,
            DIE_Y: sub_test_item_detail.DIE_Y,
            TEST_TIME: sub_test_item_detail.TEST_TIME.map(|v| v as u32),
            PART_TXT: sub_test_item_detail.PART_TXT.clone().unwrap(),
            PART_FIX: sub_test_item_detail.PART_FIX.clone().unwrap(),
            SITE: sub_test_item_detail.SITE.map(|v| v as u32),
            SITE_GRP: file_detail.SITE_GRP,
            SITE_CNT: file_detail.SITE_CNT,
            TOUCH_DOWN_ID: sub_test_item_detail.TOUCH_DOWN_ID.map(|v| v as u32),
            SITE_NUMS: &file_detail.SITE_NUMS,
            START_TIME: file_detail.START_TIME,
            END_TIME: file_detail.END_TIME,
            START_HOUR_KEY: file_detail.START_HOUR_KEY.clone(),
            START_DAY_KEY: file_detail.START_DAY_KEY.clone(),
            END_HOUR_KEY: file_detail.END_HOUR_KEY.clone(),
            END_DAY_KEY: file_detail.END_DAY_KEY.clone(),
            WAFER_ID: sub_test_item_detail.WAFER_ID.clone().unwrap(),
            WAFER_NO: sub_test_item_detail.WAFER_NO.clone().unwrap(),
            WAFER_SIZE: file_detail.WAFER_SIZE,
            WAFER_MARGIN: file_detail.WAFER_MARGIN,
            DIE_HEIGHT: file_detail.DIE_HEIGHT,
            DIE_WIDTH: file_detail.DIE_WIDTH,
            WF_UNITS: file_detail.WF_UNITS,
            WF_FLAT: &file_detail.WF_FLAT,
            CENTER_X: file_detail.CENTER_X,
            CENTER_Y: file_detail.CENTER_Y,
            CENTER_OFFSET_X: file_detail.CENTER_OFFSET_X,
            CENTER_OFFSET_Y: file_detail.CENTER_OFFSET_Y,
            CENTER_RETICLE_X: file_detail.CENTER_RETICLE_X,
            CENTER_RETICLE_Y: file_detail.CENTER_RETICLE_Y.map(|v| v as u8),
            CENTER_RETICLE_OFFSET_X: file_detail.CENTER_RETICLE_OFFSET_X,
            CENTER_RETICLE_OFFSET_Y: file_detail.CENTER_RETICLE_OFFSET_Y,
            POS_X: &file_detail.POS_X,
            POS_Y: &file_detail.POS_Y,
            DIE_CNT: file_detail.DIE_CNT,
            RETICLE_T_X: sub_test_item_detail.RETICLE_T_X,
            RETICLE_T_Y: sub_test_item_detail.RETICLE_T_Y,
            RETICLE_X: sub_test_item_detail.RETICLE_X,
            RETICLE_Y: sub_test_item_detail.RETICLE_Y,
            RETICLE_ROW: file_detail.RETICLE_ROW,
            RETICLE_COLUMN: file_detail.RETICLE_COLUMN,
            RETICLE_ROW_CENTER_OFFSET: file_detail.RETICLE_ROW_CENTER_OFFSET,
            RETICLE_COLUMN_CENTER_OFFSET: file_detail.RETICLE_COLUMN_CENTER_OFFSET,
            ORIGINAL_WAFER_SIZE: file_detail.ORIGINAL_WAFER_SIZE,
            ORIGINAL_WAFER_MARGIN: file_detail.ORIGINAL_WAFER_MARGIN,
            ORIGINAL_WF_UNITS: file_detail.ORIGINAL_WF_UNITS,
            ORIGINAL_WF_FLAT: &file_detail.ORIGINAL_WF_FLAT,
            ORIGINAL_POS_X: &file_detail.ORIGINAL_POS_X,
            ORIGINAL_POS_Y: &file_detail.ORIGINAL_POS_Y,
            ORIGINAL_DIE_WIDTH: file_detail.ORIGINAL_DIE_WIDTH,
            ORIGINAL_DIE_HEIGHT: file_detail.ORIGINAL_DIE_HEIGHT,
            ORIGINAL_RETICLE_ROW: file_detail.ORIGINAL_RETICLE_ROW,
            ORIGINAL_RETICLE_COLUMN: file_detail.ORIGINAL_RETICLE_COLUMN,
            ORIGINAL_RETICLE_ROW_CENTER_OFFSET: file_detail.ORIGINAL_RETICLE_ROW_CENTER_OFFSET,
            ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: file_detail.ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET,
            ORIGINAL_CENTER_X: file_detail.ORIGINAL_CENTER_X,
            ORIGINAL_CENTER_Y: file_detail.ORIGINAL_CENTER_Y,
            ORIGINAL_CENTER_RETICLE_X: file_detail.ORIGINAL_CENTER_RETICLE_X,
            ORIGINAL_CENTER_RETICLE_Y: file_detail.ORIGINAL_CENTER_RETICLE_Y.map(|v| v as u8),
            ORIGINAL_CENTER_OFFSET_X: file_detail.ORIGINAL_CENTER_OFFSET_X,
            ORIGINAL_CENTER_OFFSET_Y: file_detail.ORIGINAL_CENTER_OFFSET_Y,
            ORIGINAL_CENTER_RETICLE_OFFSET_X: file_detail.ORIGINAL_CENTER_RETICLE_OFFSET_X,
            ORIGINAL_CENTER_RETICLE_OFFSET_Y: file_detail.ORIGINAL_CENTER_RETICLE_OFFSET_Y,
            SITE_ID: sub_test_item_detail.SITE_ID.clone().unwrap(),
            PART_CNT: file_detail.PART_CNT,
            RTST_CNT: file_detail.RTST_CNT,
            ABRT_CNT: file_detail.ABRT_CNT,
            GOOD_CNT: file_detail.GOOD_CNT,
            FUNC_CNT: file_detail.FUNC_CNT,
            FABWF_ID: &file_detail.FABWF_ID,
            FRAME_ID: &file_detail.FRAME_ID,
            MASK_ID: &file_detail.MASK_ID,
            WAFER_USR_DESC: &file_detail.WAFER_USR_DESC,
            WAFER_EXC_DESC: &file_detail.WAFER_EXC_DESC,
            SETUP_T: file_detail.SETUP_T,
            STAT_NUM: file_detail.STAT_NUM,
            MODE_COD: &file_detail.MODE_COD,
            PROT_COD: &file_detail.PROT_COD,
            BURN_TIM: file_detail.BURN_TIM,
            CMOD_COD: &file_detail.CMOD_COD,
            EXEC_TYP: &file_detail.EXEC_TYP,
            EXEC_VER: &file_detail.EXEC_VER,
            USER_TXT: &file_detail.USER_TXT,
            AUX_FILE: &file_detail.AUX_FILE,
            PKG_TYP: &file_detail.PKG_TYP,
            FAMLY_ID: &file_detail.FAMLY_ID,
            DATE_COD: &file_detail.DATE_COD,
            FACIL_ID: &file_detail.FACIL_ID,
            FLOOR_ID: &file_detail.FLOOR_ID,
            PROC_ID: &file_detail.PROC_ID,
            OPER_FRQ: &file_detail.OPER_FRQ,
            FLOW_ID: &file_detail.FLOW_ID,
            FLOW_ID_IGNORE_TP: &file_detail.FLOW_ID_IGNORE_TP,
            SETUP_ID: &file_detail.SETUP_ID,
            DSGN_REV: &file_detail.DSGN_REV,
            ENG_ID: &file_detail.ENG_ID,
            ROM_COD: &file_detail.ROM_COD,
            SERL_NUM: &file_detail.SERL_NUM,
            SUPR_NAM: &file_detail.SUPR_NAM,
            DISP_COD: &file_detail.DISP_COD,
            LOT_USR_DESC: &file_detail.LOT_USR_DESC,
            LOT_EXC_DESC: &file_detail.LOT_EXC_DESC,
            DIB_TYP: &file_detail.DIB_TYP,
            DIB_ID: &file_detail.DIB_ID,
            CABL_TYP: &file_detail.CABL_TYP,
            CABL_ID: &file_detail.CABL_ID,
            CONT_TYP: &file_detail.CONT_TYP,
            CONT_ID: &file_detail.CONT_ID,
            LASR_TYP: &file_detail.LASR_TYP,
            LASR_ID: &file_detail.LASR_ID,
            EXTR_TYP: &file_detail.EXTR_TYP,
            EXTR_ID: &file_detail.EXTR_ID,
            RETEST_BIN_NUM: &file_detail.RETEST_BIN_NUM,
            VECT_NAM: sub_test_item_detail.VECT_NAM.clone().unwrap(),
            TIME_SET: sub_test_item_detail.TIME_SET.clone().unwrap(),
            NUM_FAIL: sub_test_item_detail.NUM_FAIL.map(|i| i as u32),
            FAIL_PIN: sub_test_item_detail.FAIL_PIN.clone().unwrap(),
            CYCL_CNT: sub_test_item_detail.CYCL_CNT.map(|i| i as u32),
            REPT_CNT: sub_test_item_detail.REPT_CNT.map(|i| i as u32),
            LONG_ATTRIBUTE_SET: sub_test_item_detail
                .LONG_ATTRIBUTE_SET
                .as_ref()
                .map(|map| map.iter().map(|(k, v)| (k.clone(), *v)).collect::<Vec<(String, i64)>>())
                .unwrap_or_default(),
            STRING_ATTRIBUTE_SET: sub_test_item_detail
                .STRING_ATTRIBUTE_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            FLOAT_ATTRIBUTE_SET: sub_test_item_detail
                .FLOAT_ATTRIBUTE_SET
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.into_decimal38_18()))
                        .collect::<Vec<(String, Decimal38_18)>>()
                })
                .unwrap_or_default(),
            UID: sub_test_item_detail.UID.clone().unwrap(),
            TEXT_DAT: DwdCommonUtil::string_value(sub_test_item_detail.TEXT_DAT.clone()),
            CREATE_HOUR_KEY: sub_test_item_detail.CREATE_HOUR_KEY.clone().unwrap(),
            CREATE_DAY_KEY: sub_test_item_detail.CREATE_DAY_KEY.clone().unwrap(),
            CREATE_TIME: sub_test_item_detail.CREATE_TIME.into_utc(),
            CREATE_USER: &file_detail.CREATE_USER,
            LOT_BUCKET: file_detail.LOT_BUCKET,
            IS_DELETE: file_detail.IS_DELETE,
            PROCESS: if file_detail.PROCESS.is_none() { EMPTY } else { &file_detail.PROCESS.as_ref().unwrap() },
            UPLOAD_TIME: DwdCommonUtil::cal_upload_time(
                file_detail.UPLOAD_TIME,
                sub_test_item_detail.CREATE_TIME.into_utc(),
            ),
            DATA_VERSION: DwdCommonUtil::cal_data_version(file_detail.DATA_VERSION),
            EFUSE_EXTRA: sub_test_item_detail
                .EFUSE_EXTRA
                .as_ref()
                .map(|map| {
                    map.iter()
                        .map(|(k, v)| (k.clone(), v.clone()))
                        .collect::<Vec<(String, String)>>()
                })
                .unwrap_or_default(),
            CHIP_ID: sub_test_item_detail.CHIP_ID.clone().unwrap(),
        }
    }
}
